import * as THREE from "three";
import { G<PERSON> } from "lil-gui";
import { RectAreaLightHelper } from "three/addons/helpers/RectAreaLightHelper.js";
import { RectAreaLightUniformsLib } from "three/addons/lights/RectAreaLightUniformsLib.js";
import { EARTH_RADIUS } from "../../../../constants";

const LIGHT_OFFSET = EARTH_RADIUS * 2;

class Lights {
  constructor(parentContainer, camera, controls, { sunCoordsRef, shouldOrbit = true, enableGUI = true }) {
    this.parentContainer = parentContainer; // 可以是 scene 或 earthGroup
    this.camera = camera;
    this.controls = controls;
    this.sunCoordsRef = sunCoordsRef;
    this.shouldOrbit = shouldOrbit;
    this.enableGUI = enableGUI;

    // Light objects
    this.directionalLight = null;
    this.ambientLight = null;
    this.rectAreaLight = null;
    this.rectAreaLightHelper = null;
    this.pointLight = null; // 新增：点光源
    this.pointLightHelper = null; // 新增：点光源辅助器
    this.lightGroup = null;

    // Light position tracking
    this.lightPos = new THREE.Vector3();
    this.shouldAnimate = false;
    this.animatePos = null;

    // GUI controls
    this.gui = null;
    this.folder = null;
    this.lightParams = {
      directionalIntensity: 2.7,
      ambientIntensity: 0.85,
      rectAreaIntensity: 4.2,
      rectAreaPower: 1.6, // 新增：面光源功率属性
      pointLightIntensity: 2.0, // 新增：点光源强度
      pointLightPower: 1.0, // 新增：点光源功率属性
      pointLightDistance: 500, // 新增：点光源距离
      pointLightDecay: 0, // 新增：点光源衰减
      directionalColor: "#ffffff",
      ambientColor: "#ffffff",
      rectAreaColor: "#00e1ff",
      pointLightColor: "#ff6600", // 新增：点光源颜色（橙色）
      lightOffset: LIGHT_OFFSET,
      rectAreaWidth: 6.5,
      rectAreaHeight: 25.5,
      rectAreaPositionX: -18,
      rectAreaPositionY: 12,
      rectAreaPositionZ: 1,
      rectAreaRotationX: -67,
      rectAreaRotationY: -50,
      rectAreaRotationZ: 63,
      pointLightPositionX: -29.5, // 新增：点光源X位置
      pointLightPositionY: 9.5, // 新增：点光源Y位置
      pointLightPositionZ: 3.5, // 新增：点光源Z位置
      showRectAreaHelper: true,
      showPointLightHelper: true, // 新增：显示点光源辅助器
      autoRotate: this.shouldOrbit,
    };

    // Initial position
    this.startPosition = new THREE.Vector3(-LIGHT_OFFSET, LIGHT_OFFSET * (this.sunCoordsRef.value?.lat / 45 || 0), 0);

    this.init();
  }

  init() {
    // Initialize RectAreaLight uniforms
    RectAreaLightUniformsLib.init();

    this.createLights();
    this.setupInitialPositions();
    this.addToScene();

    // 只有在启用 GUI 时才创建辅助器
    if (this.enableGUI) {
      this.createRectAreaLightHelperIfNeeded();
      this.createPointLightHelperIfNeeded();
      this.initGUI();
    }
  }

  createLights() {
    // Create directional light (sun)
    this.directionalLight = new THREE.DirectionalLight(this.lightParams.directionalColor, this.lightParams.directionalIntensity);
    this.directionalLight.position.copy(this.startPosition);
    this.directionalLight.castShadow = true;

    // Create rect area light (面光源)
    // 计算最终强度：基础强度 × 功率
    const finalIntensity = this.lightParams.rectAreaIntensity * this.lightParams.rectAreaPower;
    this.rectAreaLight = new THREE.RectAreaLight(this.lightParams.rectAreaColor, finalIntensity, this.lightParams.rectAreaWidth, this.lightParams.rectAreaHeight);
    this.rectAreaLight.position.set(this.lightParams.rectAreaPositionX, this.lightParams.rectAreaPositionY, this.lightParams.rectAreaPositionZ);
    this.rectAreaLight.rotation.set(
      THREE.MathUtils.degToRad(this.lightParams.rectAreaRotationX),
      THREE.MathUtils.degToRad(this.lightParams.rectAreaRotationY),
      THREE.MathUtils.degToRad(this.lightParams.rectAreaRotationZ)
    );

    // Create point light (点光源)
    // 计算最终强度：基础强度 × 功率
    const pointFinalIntensity = this.lightParams.pointLightIntensity * this.lightParams.pointLightPower;
    this.pointLight = new THREE.PointLight(this.lightParams.pointLightColor, pointFinalIntensity, this.lightParams.pointLightDistance, this.lightParams.pointLightDecay);
    this.pointLight.position.set(this.lightParams.pointLightPositionX, this.lightParams.pointLightPositionY, this.lightParams.pointLightPositionZ);
    this.pointLight.castShadow = true;

    // 面光源和点光源辅助器将在GUI初始化后根据需要创建

    // Create light group for rotation
    this.lightGroup = new THREE.Group();
    this.lightGroup.add(this.directionalLight);
    this.lightGroup.add(this.rectAreaLight);
    // this.lightGroup.add(this.pointLight);
    if (this.rectAreaLightHelper) {
      this.lightGroup.add(this.rectAreaLightHelper);
    }
    // 注意：pointLightHelper 不添加到 lightGroup 中，
    // 而是在 createPointLightHelperIfNeeded() 中直接添加到场景

    // Create ambient light
    this.ambientLight = new THREE.AmbientLight(this.lightParams.ambientColor, this.lightParams.ambientIntensity);
  }

  createRectAreaLightHelper() {
    // Create helper to visualize the rect area light
    this.rectAreaLightHelper = new RectAreaLightHelper(this.rectAreaLight);
    this.rectAreaLightHelper.visible = this.lightParams.showRectAreaHelper;
  }

  createPointLightHelper() {
    // Create helper to visualize the point light
    this.pointLightHelper = new THREE.PointLightHelper(this.pointLight, 1);
    this.pointLightHelper.visible = this.lightParams.showPointLightHelper;

    // PointLightHelper 会自动跟随点光源位置，无需手动同步
  }

  /**
   * 创建面光源辅助器（仅在GUI启用时调用）
   */
  createRectAreaLightHelperIfNeeded() {
    // 如果还没有创建辅助器，则创建
    if (!this.rectAreaLightHelper && this.rectAreaLight) {
      this.createRectAreaLightHelper();
      // 将辅助器添加到光源组中
      if (this.lightGroup && this.rectAreaLightHelper) {
        this.lightGroup.add(this.rectAreaLightHelper);
      }
    }
  }

  /**
   * 创建点光源辅助器（仅在GUI启用时调用）
   */
  createPointLightHelperIfNeeded() {
    // 如果还没有创建辅助器，则创建
    if (!this.pointLightHelper && this.pointLight) {
      this.createPointLightHelper();
      // 将辅助器直接添加到场景中，而不是光源组中
      // 这样辅助器只跟随点光源，不受组旋转的双重影响
      const scene = this.getScene();
      if (scene && this.pointLightHelper) {
        scene.add(this.pointLightHelper);
      }
    }
  }

  setupInitialPositions() {
    // Set initial camera position

    // Set initial light group rotation
    if (this.sunCoordsRef.value) {
      this.lightGroup.rotation.y = THREE.MathUtils.degToRad(this.sunCoordsRef.value.lng);
    }
  }

  addToScene() {
    if (this.parentContainer) {
      this.parentContainer.add(this.lightGroup);
      this.parentContainer.add(this.ambientLight);
    }
  }

  update() {
    if (!this.sunCoordsRef.value) return;

    // Update light position based on sun coordinates
    if (this.directionalLight && this.lightGroup) {
      this.directionalLight.position.y = this.lightParams.lightOffset * (this.sunCoordsRef.value.lat / 45);

      this.lightGroup.rotation.y = THREE.MathUtils.degToRad(this.sunCoordsRef.value.lng);
    }

    // Update global light position for other components
    if (this.directionalLight) {
      this.directionalLight.getWorldPosition(this.lightPos);

      // Store light position globally for other components to access
      // 需要找到根场景对象来存储全局数据
      const scene = this.getScene();
      if (scene) {
        if (scene.userData) {
          scene.userData.lightPos = this.lightPos;
        } else {
          scene.userData = { lightPos: this.lightPos };
        }
      }
    }

    // Update rect area light helper if needed
    if (this.rectAreaLightHelper && this.rectAreaLight) {
      // RectAreaLightHelper automatically updates with the light
      // No manual color update needed
    }

    // Update orbit controls auto-rotate speed
    if (this.controls) {
      this.controls.autoRotateSpeed = this.shouldOrbit && !this.shouldAnimate ? 0.03 : 0;
    }
  }

  getLightPosition() {
    return this.lightPos;
  }

  /**
   * 获取根场景对象
   * 如果 parentContainer 是场景，直接返回；如果是组，则向上查找场景
   */
  getScene() {
    let current = this.parentContainer;
    while (current) {
      if (current.type === "Scene") {
        return current;
      }
      current = current.parent;
    }
    return null;
  }

  setAutoRotate(enabled) {
    this.shouldOrbit = enabled;
    this.lightParams.autoRotate = enabled;
    if (this.controls) {
      this.controls.autoRotateSpeed = enabled && !this.shouldAnimate ? 0.03 : 0;
    }
  }

  /**
   * 初始化GUI控制面板
   */
  initGUI() {
    // 如果GUI被禁用，则跳过初始化
    if (!this.enableGUI) {
      return;
    }

    try {
      // 获取或创建全局GUI实例
      if (window.gui) {
        this.gui = window.gui;
      } else {
        this.gui = new GUI();
        window.gui = this.gui;
      }

      // 创建灯光控制文件夹
      this.folder = this.gui.addFolder("💡 灯光控制 (Lighting)");
      this.folder.open();

      this.setupGUIControls();
    } catch (error) {
      console.warn("Lights GUI setup warning:", error);
    }
  }

  /**
   * 设置GUI控制项
   */
  setupGUIControls() {
    if (!this.folder) return;

    // 方向光控制
    const directionalFolder = this.folder.addFolder("☀️ 方向光 (Directional Light)");
    directionalFolder.open();

    // 方向光强度
    directionalFolder
      .add(this.lightParams, "directionalIntensity", 0, 10, 0.1)
      .name("强度")
      .onChange((value) => {
        if (this.directionalLight) {
          this.directionalLight.intensity = value;
        }
      });

    // 方向光颜色
    directionalFolder
      .addColor(this.lightParams, "directionalColor")
      .name("颜色")
      .onChange((value) => {
        if (this.directionalLight) {
          this.directionalLight.color.set(value);
        }
      });

    // 环境光控制
    const ambientFolder = this.folder.addFolder("🌙 环境光 (Ambient Light)");
    ambientFolder.open();

    // 环境光强度
    ambientFolder
      .add(this.lightParams, "ambientIntensity", 0, 20, 0.05)
      .name("强度")
      .onChange((value) => {
        if (this.ambientLight) {
          this.ambientLight.intensity = value;
        }
      });

    // 环境光颜色
    ambientFolder
      .addColor(this.lightParams, "ambientColor")
      .name("颜色")
      .onChange((value) => {
        if (this.ambientLight) {
          this.ambientLight.color.set(value);
        }
      });

    // 面光源控制
    const rectAreaFolder = this.folder.addFolder("� 面光源 (Rect Area Light)");
    rectAreaFolder.open();

    // 面光源强度
    rectAreaFolder
      .add(this.lightParams, "rectAreaIntensity", 0, 100, 0.1)
      .name("强度")
      .onChange(() => {
        this.updateRectAreaLightIntensity();
      });

    // 面光源功率
    rectAreaFolder
      .add(this.lightParams, "rectAreaPower", 0, 5, 0.1)
      .name("功率")
      .onChange(() => {
        this.updateRectAreaLightIntensity();
      });

    // 面光源颜色
    rectAreaFolder
      .addColor(this.lightParams, "rectAreaColor")
      .name("颜色")
      .onChange((value) => {
        if (this.rectAreaLight) {
          this.rectAreaLight.color.set(value);
        }
      });

    // 面光源宽度
    rectAreaFolder
      .add(this.lightParams, "rectAreaWidth", 1, 50, 0.5)
      .name("宽度")
      .onChange((value) => {
        if (this.rectAreaLight) {
          this.rectAreaLight.width = value;
        }
      });

    // 面光源高度
    rectAreaFolder
      .add(this.lightParams, "rectAreaHeight", 1, 50, 0.5)
      .name("高度")
      .onChange((value) => {
        if (this.rectAreaLight) {
          this.rectAreaLight.height = value;
        }
      });

    // 面光源位置控制
    const rectAreaPositionFolder = rectAreaFolder.addFolder("位置控制");
    rectAreaPositionFolder.open();

    // X坐标
    rectAreaPositionFolder
      .add(this.lightParams, "rectAreaPositionX", -50, 50, 0.5)
      .name("X坐标")
      .onChange(() => {
        this.updateRectAreaLightPosition();
      });

    // Y坐标
    rectAreaPositionFolder
      .add(this.lightParams, "rectAreaPositionY", -50, 50, 0.5)
      .name("Y坐标")
      .onChange(() => {
        this.updateRectAreaLightPosition();
      });

    // Z坐标
    rectAreaPositionFolder
      .add(this.lightParams, "rectAreaPositionZ", -50, 50, 0.5)
      .name("Z坐标")
      .onChange(() => {
        this.updateRectAreaLightPosition();
      });

    // 面光源旋转控制
    const rectAreaRotationFolder = rectAreaFolder.addFolder("旋转控制");
    rectAreaRotationFolder.open();

    // X轴旋转
    rectAreaRotationFolder
      .add(this.lightParams, "rectAreaRotationX", -180, 180, 1)
      .name("X轴旋转")
      .onChange(() => {
        this.updateRectAreaLightRotation();
      });

    // Y轴旋转
    rectAreaRotationFolder
      .add(this.lightParams, "rectAreaRotationY", -180, 180, 1)
      .name("Y轴旋转")
      .onChange(() => {
        this.updateRectAreaLightRotation();
      });

    // Z轴旋转
    rectAreaRotationFolder
      .add(this.lightParams, "rectAreaRotationZ", -180, 180, 1)
      .name("Z轴旋转")
      .onChange(() => {
        this.updateRectAreaLightRotation();
      });

    // 显示/隐藏辅助器
    rectAreaFolder
      .add(this.lightParams, "showRectAreaHelper")
      .name("显示辅助器")
      .onChange((value) => {
        if (this.rectAreaLightHelper) {
          this.rectAreaLightHelper.visible = value;
        }
      });

    // 点光源控制
    const pointLightFolder = this.folder.addFolder("💡 点光源 (Point Light)");
    pointLightFolder.open();

    // 点光源强度
    pointLightFolder
      .add(this.lightParams, "pointLightIntensity", 0, 100, 0.1)
      .name("强度")
      .onChange(() => {
        this.updatePointLightIntensity();
      });

    // 点光源功率
    pointLightFolder
      .add(this.lightParams, "pointLightPower", 0, 50, 0.1)
      .name("功率")
      .onChange(() => {
        this.updatePointLightIntensity();
      });

    // 点光源颜色
    pointLightFolder
      .addColor(this.lightParams, "pointLightColor")
      .name("颜色")
      .onChange((value) => {
        if (this.pointLight) {
          this.pointLight.color.set(value);
        }
      });

    // 点光源距离
    pointLightFolder
      .add(this.lightParams, "pointLightDistance", 0, 1000, 1)
      .name("距离")
      .onChange((value) => {
        if (this.pointLight) {
          this.pointLight.distance = value;
        }
      });

    // 点光源衰减
    pointLightFolder
      .add(this.lightParams, "pointLightDecay", 0, 5, 0.1)
      .name("衰减")
      .onChange((value) => {
        if (this.pointLight) {
          this.pointLight.decay = value;
        }
      });

    // 点光源位置控制
    const pointLightPositionFolder = pointLightFolder.addFolder("位置 (Position)");
    pointLightPositionFolder.open();

    // X位置
    pointLightPositionFolder
      .add(this.lightParams, "pointLightPositionX", -50, 50, 0.5)
      .name("X位置")
      .onChange(() => {
        this.updatePointLightPosition();
      });

    // Y位置
    pointLightPositionFolder
      .add(this.lightParams, "pointLightPositionY", -50, 50, 0.5)
      .name("Y位置")
      .onChange(() => {
        this.updatePointLightPosition();
      });

    // Z位置
    pointLightPositionFolder
      .add(this.lightParams, "pointLightPositionZ", -50, 50, 0.5)
      .name("Z位置")
      .onChange(() => {
        this.updatePointLightPosition();
      });

    // 显示/隐藏点光源辅助器
    pointLightFolder
      .add(this.lightParams, "showPointLightHelper")
      .name("显示辅助器")
      .onChange((value) => {
        if (this.pointLightHelper) {
          this.pointLightHelper.visible = value;
        }
      });

    // 位置和控制
    const controlFolder = this.folder.addFolder("🎮 位置控制 (Position Control)");
    controlFolder.open();

    // 光源偏移距离
    controlFolder
      .add(this.lightParams, "lightOffset", EARTH_RADIUS, EARTH_RADIUS * 5, EARTH_RADIUS * 0.1)
      .name("光源距离")
      .onChange((value) => {
        this.updateLightPosition(value);
      });

    // 自动旋转开关
    controlFolder
      .add(this.lightParams, "autoRotate")
      .name("自动旋转")
      .onChange((value) => {
        this.setAutoRotate(value);
      });
  }

  /**
   * 更新光源位置
   */
  updateLightPosition(offset) {
    if (!this.directionalLight || !this.sunCoordsRef.value) return;

    // 更新起始位置
    this.startPosition.set(-offset, offset * (this.sunCoordsRef.value.lat / 45 || 0), 0);

    // 更新当前位置
    this.directionalLight.position.copy(this.startPosition);
    this.directionalLight.position.y = offset * (this.sunCoordsRef.value.lat / 45);
  }

  /**
   * 更新面光源位置
   */
  updateRectAreaLightPosition() {
    if (!this.rectAreaLight) return;

    // 更新面光源位置
    this.rectAreaLight.position.set(this.lightParams.rectAreaPositionX, this.lightParams.rectAreaPositionY, this.lightParams.rectAreaPositionZ);
  }

  /**
   * 更新面光源旋转
   */
  updateRectAreaLightRotation() {
    if (!this.rectAreaLight) return;

    // 更新面光源旋转
    this.rectAreaLight.rotation.set(
      THREE.MathUtils.degToRad(this.lightParams.rectAreaRotationX),
      THREE.MathUtils.degToRad(this.lightParams.rectAreaRotationY),
      THREE.MathUtils.degToRad(this.lightParams.rectAreaRotationZ)
    );
  }

  /**
   * 更新面光源强度（基于强度和功率的乘积）
   */
  updateRectAreaLightIntensity() {
    if (!this.rectAreaLight) return;

    // 计算最终强度：基础强度 × 功率
    const finalIntensity = this.lightParams.rectAreaIntensity * this.lightParams.rectAreaPower;
    this.rectAreaLight.intensity = finalIntensity;
  }

  /**
   * 更新点光源位置
   */
  updatePointLightPosition() {
    if (!this.pointLight) return;

    // 更新点光源位置
    this.pointLight.position.set(this.lightParams.pointLightPositionX, this.lightParams.pointLightPositionY, this.lightParams.pointLightPositionZ);

    // PointLightHelper 会自动跟随点光源位置，无需手动同步
  }

  /**
   * 更新点光源强度（基于强度和功率的乘积）
   */
  updatePointLightIntensity() {
    if (!this.pointLight) return;

    // 计算最终强度：基础强度 × 功率
    const finalIntensity = this.lightParams.pointLightIntensity * this.lightParams.pointLightPower;
    this.pointLight.intensity = finalIntensity;
  }

  destroy() {
    // Remove GUI
    if (this.folder) {
      this.folder.destroy();
      this.folder = null;
    }

    // Remove from parent container
    if (this.parentContainer) {
      if (this.lightGroup) {
        this.parentContainer.remove(this.lightGroup);
      }
      if (this.ambientLight) {
        this.parentContainer.remove(this.ambientLight);
      }
    }

    // Remove point light helper from scene if it exists
    if (this.pointLightHelper) {
      const scene = this.getScene();
      if (scene) {
        scene.remove(this.pointLightHelper);
      }
    }

    // Dispose of lights
    if (this.directionalLight) {
      this.directionalLight.dispose();
    }
    if (this.ambientLight) {
      this.ambientLight.dispose();
    }
    if (this.rectAreaLight) {
      this.rectAreaLight.dispose();
    }
    if (this.pointLight) {
      this.pointLight.dispose();
    }

    // Dispose of light helpers
    if (this.rectAreaLightHelper) {
      this.rectAreaLightHelper.dispose();
    }
    if (this.pointLightHelper) {
      this.pointLightHelper.dispose();
    }

    // Clear references
    this.directionalLight = null;
    this.ambientLight = null;
    this.rectAreaLight = null;
    this.rectAreaLightHelper = null;
    this.pointLight = null;
    this.pointLightHelper = null;
    this.lightGroup = null;
    this.lightPos = null;
    this.gui = null;
  }
}

// Export both named and default exports for flexibility
export { Lights };
export default Lights;
