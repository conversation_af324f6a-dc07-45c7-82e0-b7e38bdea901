import * as THREE from "three";
import { G<PERSON> } from "lil-gui";
import { EARTH_FRAGMENTS, EARTH_RADIUS, PATHS } from "../../../../constants/index.js";
import { texturePreloader } from "../../utils/TexturePreloader.js";

class EarthDay {
  constructor(scene, { onLoad, renderer, enableGUI = true } = {}) {
    this.scene = scene;
    this.onLoad = onLoad;
    this.renderer = renderer;
    this.enableGUI = enableGUI;
    this.mesh = null;
    this.geometry = null;
    this.material = null;
    this.textures = {
      day: null,
      bump: null,
      specular: null,
    };

    // GUI 相关属性
    this.gui = null;
    this.folder = null;

    // 纹理上传相关属性
    this.originalTextures = {
      day: null,
      bump: null,
      specular: null,
    }; // 保存原始纹理的引用
    this.customTextures = {
      day: null,
      bump: null,
      specular: null,
    }; // 用户上传的自定义纹理

    // Material parameters for GUI control
    this.materialParams = {
      // 基础属性
      color: "#ffffff",
      opacity: 1.0,
      transparent: true,

      // 物理属性
      metalness: 0,
      roughness: 1.0,

      // 凹凸贴图
      bumpScale: 12.4,

      // 各向异性
      anisotropy: 0.0,
      anisotropyRotation: 0.0,

      // 清漆层
      clearcoat: 0.0,
      clearcoatRoughness: 0.0,

      // 折射率
      ior: 2.33,
      reflectivity: 1,

      // 彩虹色
      iridescence: 0.0,
      iridescenceIOR: 1.3,

      // 光泽
      sheen: 0.0,
      sheenRoughness: 1.0,
      sheenColor: "#000000",

      // 镜面反射
      specularIntensity: 1.0,
      specularColor: "#ffffff",

      // 透射
      transmission: 0.0,
      thickness: 0.0,
      attenuationDistance: Infinity,
      attenuationColor: "#ffffff",
      dispersion: 0.0,
    };

    this.init();
  }

  async init() {
    await this.loadTextures();
    this.createGeometry();
    this.createMaterial();
    this.createMesh();
    this.addToScene();

    // Initialize GUI controls
    if (this.enableGUI) {
      this.initGUI();
    }

    // Call onLoad callback if provided
    if (this.onLoad) {
      this.onLoad();
    }
  }

  async loadTextures() {
    try {
      // 优先使用预加载的纹理
      const preloadedTextures = texturePreloader.getAllTextures();

      if (preloadedTextures.earthMap && preloadedTextures.bumpMap && preloadedTextures.specularMap) {
        console.log("EarthDay: 使用预加载的纹理");

        this.textures.day = preloadedTextures.earthMap;
        this.textures.bump = preloadedTextures.bumpMap;
        this.textures.specular = preloadedTextures.specularMap;

        // 保存原始纹理的引用
        this.originalTextures.day = preloadedTextures.earthMap;
        this.originalTextures.bump = preloadedTextures.bumpMap;
        this.originalTextures.specular = preloadedTextures.specularMap;
      } else {
        console.log("EarthDay: 预加载纹理不可用，使用异步加载");

        // 如果预加载纹理不可用，使用传统的异步加载方式
        const textureLoader = new THREE.TextureLoader();
        const [dayTexture, bumpTexture, specularTexture] = await Promise.all([
          this.loadTexture(textureLoader, PATHS.earthMap),
          this.loadTexture(textureLoader, PATHS.bumpMap),
          this.loadTexture(textureLoader, PATHS.specularMap),
        ]);

        this.textures.day = dayTexture;
        this.textures.bump = bumpTexture;
        this.textures.specular = specularTexture;

        // 保存原始纹理的引用
        this.originalTextures.day = dayTexture;
        this.originalTextures.bump = bumpTexture;
        this.originalTextures.specular = specularTexture;
      }

      // Set color space for proper color rendering
      this.textures.day.colorSpace = THREE.SRGBColorSpace; // Color texture needs sRGB
      this.textures.bump.colorSpace = THREE.NoColorSpace; // Data texture, no color space
      this.textures.specular.colorSpace = THREE.NoColorSpace; // Data texture, no color space

      // Set anisotropy for better quality - get actual max from renderer
      const maxAnisotropy = this.renderer?.capabilities?.getMaxAnisotropy?.() || 16;
      this.textures.day.anisotropy = maxAnisotropy;
      this.textures.bump.anisotropy = maxAnisotropy;
      this.textures.specular.anisotropy = maxAnisotropy;

      console.log("EarthDay: 纹理加载完成");
    } catch (error) {
      console.error("Error loading EarthDay textures:", error);
    }
  }

  loadTexture(loader, url) {
    return new Promise((resolve, reject) => {
      loader.load(
        url,
        (texture) => resolve(texture),
        undefined,
        (error) => reject(error)
      );
    });
  }

  createGeometry() {
    this.geometry = new THREE.SphereGeometry(EARTH_RADIUS, EARTH_FRAGMENTS, EARTH_FRAGMENTS);
  }

  createMaterial() {
    this.material = new THREE.MeshPhysicalMaterial({
      // 贴图
      map: this.textures.day,
      bumpMap: this.textures.bump,
      specularMap: this.textures.specular,

      // 基础属性
      color: new THREE.Color(this.materialParams.color),
      opacity: this.materialParams.opacity,
      transparent: this.materialParams.transparent,

      // 物理属性
      metalness: this.materialParams.metalness,
      roughness: this.materialParams.roughness,

      // 凹凸贴图
      bumpScale: this.materialParams.bumpScale,

      // 各向异性
      anisotropy: this.materialParams.anisotropy,
      anisotropyRotation: this.materialParams.anisotropyRotation,

      // 清漆层
      clearcoat: this.materialParams.clearcoat,
      clearcoatRoughness: this.materialParams.clearcoatRoughness,

      // 折射率
      ior: this.materialParams.ior,
      reflectivity: this.materialParams.reflectivity,

      // 彩虹色
      iridescence: this.materialParams.iridescence,
      iridescenceIOR: this.materialParams.iridescenceIOR,

      // 光泽
      sheen: this.materialParams.sheen,
      sheenRoughness: this.materialParams.sheenRoughness,
      sheenColor: new THREE.Color(this.materialParams.sheenColor),

      // 镜面反射
      specularIntensity: this.materialParams.specularIntensity,
      specularColor: new THREE.Color(this.materialParams.specularColor),

      // 透射
      transmission: this.materialParams.transmission,
      thickness: this.materialParams.thickness,
      attenuationDistance: this.materialParams.attenuationDistance,
      attenuationColor: new THREE.Color(this.materialParams.attenuationColor),
      dispersion: this.materialParams.dispersion,
    });
    //phon
    this.material = new THREE.MeshPhongMaterial({
      map: this.textures.day,
      bumpMap: this.textures.bump,
      specularMap: this.textures.specular,

      // 凹凸贴图
      bumpScale: this.materialParams.bumpScale,
    });
  }

  createMesh() {
    this.mesh = new THREE.Mesh(this.geometry, this.material);
    this.mesh.castShadow = true;
    this.mesh.scale.set(0.999, 0.999, 0.999);
    this.mesh.rotation.y = Math.PI;
    this.mesh.renderOrder = 0; // 地球作为基础层

    const sphere = new THREE.Mesh(new THREE.SphereGeometry(1, 4, 2), new THREE.MeshBasicMaterial({ color: 0xff0000 }));
    sphere.scale.set(1.2, 1.2, 1.2);
    this.scene.add(sphere);
  }

  addToScene() {
    if (this.scene && this.mesh) {
      // this.mesh.visible = false;
      this.scene.add(this.mesh);
    }
  }

  /**
   * 上传并替换指定类型的纹理
   * @param {File} file - 用户上传的图片文件
   * @param {string} textureType - 纹理类型 ('day', 'bump', 'specular')
   * @returns {Promise<boolean>} 是否成功替换纹理
   */
  async uploadTexture(file, textureType) {
    try {
      // 验证文件类型
      if (!file || !file.type.startsWith("image/")) {
        throw new Error("请选择有效的图片文件");
      }

      // 验证纹理类型
      if (!["day", "bump", "specular"].includes(textureType)) {
        throw new Error("无效的纹理类型");
      }

      // 创建文件URL
      const imageUrl = URL.createObjectURL(file);

      // 加载新纹理
      const textureLoader = new THREE.TextureLoader();
      const newTexture = await this.loadTextureFromUrl(textureLoader, imageUrl);

      // 设置纹理属性
      const maxAnisotropy = this.renderer?.capabilities?.getMaxAnisotropy?.() || 16;
      newTexture.anisotropy = maxAnisotropy;
      newTexture.wrapS = THREE.RepeatWrapping;
      newTexture.wrapT = THREE.RepeatWrapping;

      // 设置颜色空间
      if (textureType === "day") {
        newTexture.colorSpace = THREE.SRGBColorSpace; // 颜色纹理需要sRGB
      } else {
        newTexture.colorSpace = THREE.NoColorSpace; // 数据纹理，无颜色空间
      }

      // 释放之前的自定义纹理（如果存在）
      if (this.customTextures[textureType]) {
        this.customTextures[textureType].dispose();
      }

      // 保存新的自定义纹理
      this.customTextures[textureType] = newTexture;

      // 更新材质中的纹理
      this.textures[textureType] = newTexture;
      this.updateMaterialTexture(textureType, newTexture);

      // 清理临时URL
      URL.revokeObjectURL(imageUrl);

      console.log(`${textureType}纹理上传成功`);
      return true;
    } catch (error) {
      console.error(`${textureType}纹理上传失败:`, error);
      throw error;
    }
  }

  /**
   * 从URL加载纹理（Promise版本）
   * @param {THREE.TextureLoader} loader - 纹理加载器
   * @param {string} url - 纹理URL
   * @returns {Promise<THREE.Texture>} 加载的纹理
   */
  loadTextureFromUrl(loader, url) {
    return new Promise((resolve, reject) => {
      loader.load(
        url,
        (texture) => resolve(texture),
        undefined,
        (error) => reject(error)
      );
    });
  }

  /**
   * 更新材质中的指定纹理
   * @param {string} textureType - 纹理类型
   * @param {THREE.Texture} texture - 新纹理
   */
  updateMaterialTexture(textureType, texture) {
    if (!this.material) return;

    switch (textureType) {
      case "day":
        this.material.map = texture;
        break;
      case "bump":
        this.material.bumpMap = texture;
        break;
      case "specular":
        this.material.specularMap = texture;
        break;
    }

    this.material.needsUpdate = true;
  }

  /**
   * 恢复指定类型的原始纹理
   * @param {string} textureType - 纹理类型 ('day', 'bump', 'specular')
   */
  restoreOriginalTexture(textureType) {
    if (!this.originalTextures[textureType]) {
      console.warn(`没有找到原始${textureType}纹理`);
      return;
    }

    // 释放自定义纹理
    if (this.customTextures[textureType]) {
      this.customTextures[textureType].dispose();
      this.customTextures[textureType] = null;
    }

    // 恢复原始纹理
    this.textures[textureType] = this.originalTextures[textureType];
    this.updateMaterialTexture(textureType, this.originalTextures[textureType]);

    console.log(`已恢复原始${textureType}纹理`);
  }

  /**
   * 恢复所有原始纹理
   */
  restoreAllOriginalTextures() {
    ["day", "bump", "specular"].forEach((textureType) => {
      this.restoreOriginalTexture(textureType);
    });
  }

  /**
   * 初始化GUI控制面板
   */
  initGUI() {
    try {
      // 获取或创建全局GUI实例
      if (window.gui) {
        this.gui = window.gui;
      } else {
        this.gui = new GUI();
        window.gui = this.gui;
      }

      // 创建地球材质控制文件夹
      this.folder = this.gui.addFolder("🌍 地球材质 (Earth Material)");
      this.folder.open();

      this.setupGUIControls();
    } catch (error) {
      console.warn("EarthDay GUI setup warning:", error);
    }
  }

  /**
   * 设置GUI控制项
   */
  setupGUIControls() {
    if (!this.folder || !this.material) return;

    // 基础属性文件夹
    const basicFolder = this.folder.addFolder("🎨 基础属性 (Basic Properties)");
    basicFolder.open();

    // 颜色控制
    basicFolder
      .addColor(this.materialParams, "color")
      .name("颜色 (Color)")
      .onChange((value) => {
        this.material.color.set(value);
      });

    // 透明度控制
    basicFolder
      .add(this.materialParams, "opacity", 0, 1, 0.01)
      .name("透明度 (Opacity)")
      .onChange((value) => {
        this.material.opacity = value;
      });

    // 透明开关
    basicFolder
      .add(this.materialParams, "transparent")
      .name("透明 (Transparent)")
      .onChange((value) => {
        this.material.transparent = value;
      });

    // 物理属性文件夹
    const physicalFolder = this.folder.addFolder("⚡ 物理属性 (Physical Properties)");
    physicalFolder.open();

    // 金属度
    physicalFolder
      .add(this.materialParams, "metalness", 0, 1, 0.01)
      .name("金属度 (Metalness)")
      .onChange((value) => {
        this.material.metalness = value;
      });

    // 粗糙度
    physicalFolder
      .add(this.materialParams, "roughness", 0, 1, 0.01)
      .name("粗糙度 (Roughness)")
      .onChange((value) => {
        this.material.roughness = value;
      });

    // 凹凸贴图强度
    physicalFolder
      .add(this.materialParams, "bumpScale", 0, 50, 0.1)
      .name("凹凸强度 (Bump Scale)")
      .onChange((value) => {
        this.material.bumpScale = value;
      });

    // 折射率
    physicalFolder
      .add(this.materialParams, "ior", 1, 2.333, 0.01)
      .name("折射率 (IOR)")
      .onChange((value) => {
        this.material.ior = value;
      });

    // 反射率
    physicalFolder
      .add(this.materialParams, "reflectivity", 0, 1, 0.01)
      .name("反射率 (Reflectivity)")
      .onChange((value) => {
        this.material.reflectivity = value;
      });

    // 各向异性文件夹
    const anisotropyFolder = this.folder.addFolder("🔄 各向异性 (Anisotropy)");

    // 各向异性强度
    anisotropyFolder
      .add(this.materialParams, "anisotropy", 0, 1, 0.01)
      .name("强度 (Strength)")
      .onChange((value) => {
        this.material.anisotropy = value;
      });

    // 各向异性旋转
    anisotropyFolder
      .add(this.materialParams, "anisotropyRotation", 0, Math.PI * 2, 0.01)
      .name("旋转 (Rotation)")
      .onChange((value) => {
        this.material.anisotropyRotation = value;
      });

    // 清漆层文件夹
    const clearcoatFolder = this.folder.addFolder("✨ 清漆层 (Clearcoat)");

    // 清漆强度
    clearcoatFolder
      .add(this.materialParams, "clearcoat", 0, 1, 0.01)
      .name("强度 (Intensity)")
      .onChange((value) => {
        this.material.clearcoat = value;
      });

    // 清漆粗糙度
    clearcoatFolder
      .add(this.materialParams, "clearcoatRoughness", 0, 1, 0.01)
      .name("粗糙度 (Roughness)")
      .onChange((value) => {
        this.material.clearcoatRoughness = value;
      });

    // 彩虹色文件夹
    const iridescenceFolder = this.folder.addFolder("🌈 彩虹色 (Iridescence)");

    // 彩虹色强度
    iridescenceFolder
      .add(this.materialParams, "iridescence", 0, 1, 0.01)
      .name("强度 (Intensity)")
      .onChange((value) => {
        this.material.iridescence = value;
      });

    // 彩虹色折射率
    iridescenceFolder
      .add(this.materialParams, "iridescenceIOR", 1, 2.333, 0.01)
      .name("折射率 (IOR)")
      .onChange((value) => {
        this.material.iridescenceIOR = value;
      });

    // 光泽文件夹
    const sheenFolder = this.folder.addFolder("🧶 光泽 (Sheen)");

    // 光泽强度
    sheenFolder
      .add(this.materialParams, "sheen", 0, 1, 0.01)
      .name("强度 (Intensity)")
      .onChange((value) => {
        this.material.sheen = value;
      });

    // 光泽粗糙度
    sheenFolder
      .add(this.materialParams, "sheenRoughness", 0, 1, 0.01)
      .name("粗糙度 (Roughness)")
      .onChange((value) => {
        this.material.sheenRoughness = value;
      });

    // 光泽颜色
    sheenFolder
      .addColor(this.materialParams, "sheenColor")
      .name("颜色 (Color)")
      .onChange((value) => {
        this.material.sheenColor.set(value);
      });

    // 镜面反射文件夹
    const specularFolder = this.folder.addFolder("💎 镜面反射 (Specular)");

    // 镜面反射强度
    specularFolder
      .add(this.materialParams, "specularIntensity", 0, 1, 0.01)
      .name("强度 (Intensity)")
      .onChange((value) => {
        this.material.specularIntensity = value;
      });

    // 镜面反射颜色
    specularFolder
      .addColor(this.materialParams, "specularColor")
      .name("颜色 (Color)")
      .onChange((value) => {
        this.material.specularColor.set(value);
      });

    // 透射文件夹
    const transmissionFolder = this.folder.addFolder("🔍 透射 (Transmission)");

    // 透射强度
    transmissionFolder
      .add(this.materialParams, "transmission", 0, 1, 0.01)
      .name("强度 (Intensity)")
      .onChange((value) => {
        this.material.transmission = value;
      });

    // 厚度
    transmissionFolder
      .add(this.materialParams, "thickness", 0, 10, 0.01)
      .name("厚度 (Thickness)")
      .onChange((value) => {
        this.material.thickness = value;
      });

    // 衰减距离
    transmissionFolder
      .add(this.materialParams, "attenuationDistance", 0.1, 100, 0.1)
      .name("衰减距离 (Attenuation Distance)")
      .onChange((value) => {
        this.material.attenuationDistance = value;
      });

    // 衰减颜色
    transmissionFolder
      .addColor(this.materialParams, "attenuationColor")
      .name("衰减颜色 (Attenuation Color)")
      .onChange((value) => {
        this.material.attenuationColor.set(value);
      });

    // 色散
    transmissionFolder
      .add(this.materialParams, "dispersion", 0, 1, 0.01)
      .name("色散 (Dispersion)")
      .onChange((value) => {
        this.material.dispersion = value;
      });

    // 预设按钮文件夹
    const presetsFolder = this.folder.addFolder("🎯 预设 (Presets)");
    presetsFolder.open();

    const presets = {
      resetToDefault: () => this.resetToDefault(),
      earthLike: () => this.applyEarthPreset(),
      metallic: () => this.applyMetallicPreset(),
      glass: () => this.applyGlassPreset(),
      fabric: () => this.applyFabricPreset(),
    };

    presetsFolder.add(presets, "resetToDefault").name("🔄 重置默认");
    presetsFolder.add(presets, "earthLike").name("🌍 地球风格");
    presetsFolder.add(presets, "metallic").name("⚡ 金属风格");
    presetsFolder.add(presets, "glass").name("🔍 玻璃风格");
    presetsFolder.add(presets, "fabric").name("🧶 织物风格");

    // 添加纹理上传控件
    this.setupTextureUploadControls();
  }

  /**
   * 设置纹理上传控件
   */
  setupTextureUploadControls() {
    if (!this.folder) return;

    // 创建纹理上传子文件夹
    const uploadFolder = this.folder.addFolder("📁 纹理上传 (Texture Upload)");
    uploadFolder.open();

    // 创建隐藏的文件输入元素
    this.fileInputs = {};
    ["day", "bump", "specular"].forEach((textureType) => {
      const fileInput = document.createElement("input");
      fileInput.type = "file";
      fileInput.accept = "image/*";
      fileInput.style.display = "none";
      document.body.appendChild(fileInput);
      this.fileInputs[textureType] = fileInput;
    });

    // 白天纹理上传控件
    const dayFolder = uploadFolder.addFolder("☀️ 白天纹理 (Day Texture)");
    this.setupSingleTextureUpload(dayFolder, "day", "白天纹理");

    // 凹凸纹理上传控件
    const bumpFolder = uploadFolder.addFolder("🏔️ 凹凸纹理 (Bump Map)");
    this.setupSingleTextureUpload(bumpFolder, "bump", "凹凸纹理");

    // 镜面纹理上传控件
    const specularFolder = uploadFolder.addFolder("💎 镜面纹理 (Specular Map)");
    this.setupSingleTextureUpload(specularFolder, "specular", "镜面纹理");

    // 全局控制
    const globalControls = {
      restoreAllOriginal: () => {
        this.restoreAllOriginalTextures();
      },
    };

    uploadFolder.add(globalControls, "restoreAllOriginal").name("🔄 恢复所有原始纹理");

    // 添加使用说明
    const infoFolder = uploadFolder.addFolder("ℹ️ 使用说明");
    const info = {
      info1: "白天纹理：地球表面的颜色贴图",
      info2: "凹凸纹理：表面高度信息（黑白图）",
      info3: "镜面纹理：反射强度信息（黑白图）",
      info4: "建议使用 2048x1024 或更高分辨率",
      info5: "支持 JPG, PNG, WebP 等图片格式",
    };

    // 由于lil-gui不直接支持只读文本，我们使用按钮来显示信息
    Object.entries(info).forEach(([key, text]) => {
      const infoControl = {};
      infoControl[key] = () => {
        console.log(text);
      };
      infoFolder.add(infoControl, key).name(text).disable();
    });
  }

  /**
   * 设置单个纹理类型的上传控件
   * @param {Object} folder - GUI文件夹
   * @param {string} textureType - 纹理类型
   * @param {string} displayName - 显示名称
   */
  setupSingleTextureUpload(folder, textureType, displayName) {
    const fileInput = this.fileInputs[textureType];

    // 上传按钮控件
    const uploadControls = {
      upload: () => {
        fileInput.click();
      },
      restore: () => {
        this.restoreOriginalTexture(textureType);
      },
    };

    // 文件选择事件处理
    fileInput.addEventListener("change", async (event) => {
      const file = event.target.files[0];
      if (file) {
        try {
          console.log(`开始上传${displayName}...`);

          await this.uploadTexture(file, textureType);

          console.log(`${displayName}上传完成！`);
          this.showUploadSuccess(displayName);
        } catch (error) {
          console.error(`${displayName}上传失败:`, error);
          this.showUploadError(displayName, error.message);
        }

        // 清空文件输入，允许重复选择同一文件
        fileInput.value = "";
      }
    });

    // 添加上传按钮
    folder.add(uploadControls, "upload").name(`📤 上传${displayName}`);

    // 添加恢复原始纹理按钮
    folder.add(uploadControls, "restore").name(`🔄 恢复原始${displayName}`);
  }

  /**
   * 显示上传成功提示
   * @param {string} textureType - 纹理类型
   */
  showUploadSuccess(textureType) {
    console.log(`✅ ${textureType}上传成功！`);

    if (typeof window !== "undefined" && window.alert) {
      setTimeout(() => {
        console.log(`${textureType}已成功替换`);
      }, 100);
    }
  }

  /**
   * 显示上传错误提示
   * @param {string} textureType - 纹理类型
   * @param {string} message - 错误消息
   */
  showUploadError(textureType, message) {
    console.error(`❌ ${textureType}上传失败:`, message);

    if (typeof window !== "undefined" && window.alert) {
      setTimeout(() => {
        console.error(`${textureType}上传失败:`, message);
      }, 100);
    }
  }

  /**
   * 重置为默认值
   */
  resetToDefault() {
    this.materialParams = {
      color: "#ffffff",
      opacity: 1.0,
      transparent: true,
      metalness: 0.8,
      roughness: 1.0,
      bumpScale: 15.75,
      anisotropy: 0.0,
      anisotropyRotation: 0.0,
      clearcoat: 0.0,
      clearcoatRoughness: 0.0,
      ior: 1.5,
      reflectivity: 0.5,
      iridescence: 0.0,
      iridescenceIOR: 1.3,
      sheen: 0.0,
      sheenRoughness: 1.0,
      sheenColor: "#000000",
      specularIntensity: 1.0,
      specularColor: "#ffffff",
      transmission: 0.0,
      thickness: 0.0,
      attenuationDistance: Infinity,
      attenuationColor: "#ffffff",
      dispersion: 0.0,
    };
    this.updateMaterialFromParams();
    this.updateGUIFromParams();
  }

  /**
   * 应用地球风格预设
   */
  applyEarthPreset() {
    Object.assign(this.materialParams, {
      metalness: 0.1,
      roughness: 0.8,
      bumpScale: 15.75,
      clearcoat: 0.0,
      transmission: 0.0,
      sheen: 0.0,
    });
    this.updateMaterialFromParams();
    this.updateGUIFromParams();
  }

  /**
   * 应用金属风格预设
   */
  applyMetallicPreset() {
    Object.assign(this.materialParams, {
      metalness: 1.0,
      roughness: 0.2,
      anisotropy: 0.8,
      clearcoat: 0.5,
      clearcoatRoughness: 0.1,
    });
    this.updateMaterialFromParams();
    this.updateGUIFromParams();
  }

  /**
   * 应用玻璃风格预设
   */
  applyGlassPreset() {
    Object.assign(this.materialParams, {
      metalness: 0.0,
      roughness: 0.0,
      transmission: 0.9,
      thickness: 1.0,
      ior: 1.5,
      opacity: 1.0,
      transparent: true,
    });
    this.updateMaterialFromParams();
    this.updateGUIFromParams();
  }

  /**
   * 应用织物风格预设
   */
  applyFabricPreset() {
    Object.assign(this.materialParams, {
      metalness: 0.0,
      roughness: 1.0,
      sheen: 1.0,
      sheenRoughness: 0.8,
      sheenColor: "#ffffff",
    });
    this.updateMaterialFromParams();
    this.updateGUIFromParams();
  }

  /**
   * 从参数更新材质
   */
  updateMaterialFromParams() {
    if (!this.material) return;

    this.material.color.set(this.materialParams.color);
    this.material.opacity = this.materialParams.opacity;
    this.material.transparent = this.materialParams.transparent;
    this.material.metalness = this.materialParams.metalness;
    this.material.roughness = this.materialParams.roughness;
    this.material.bumpScale = this.materialParams.bumpScale;
    this.material.anisotropy = this.materialParams.anisotropy;
    this.material.anisotropyRotation = this.materialParams.anisotropyRotation;
    this.material.clearcoat = this.materialParams.clearcoat;
    this.material.clearcoatRoughness = this.materialParams.clearcoatRoughness;
    this.material.ior = this.materialParams.ior;
    this.material.reflectivity = this.materialParams.reflectivity;
    this.material.iridescence = this.materialParams.iridescence;
    this.material.iridescenceIOR = this.materialParams.iridescenceIOR;
    this.material.sheen = this.materialParams.sheen;
    this.material.sheenRoughness = this.materialParams.sheenRoughness;
    this.material.sheenColor.set(this.materialParams.sheenColor);
    this.material.specularIntensity = this.materialParams.specularIntensity;
    this.material.specularColor.set(this.materialParams.specularColor);
    this.material.transmission = this.materialParams.transmission;
    this.material.thickness = this.materialParams.thickness;
    this.material.attenuationDistance = this.materialParams.attenuationDistance;
    this.material.attenuationColor.set(this.materialParams.attenuationColor);
    this.material.dispersion = this.materialParams.dispersion;

    this.material.needsUpdate = true;
  }

  /**
   * 从参数更新GUI显示
   */
  updateGUIFromParams() {
    if (!this.folder) return;

    // 遍历所有控制器并更新显示值
    this.folder.controllersRecursive().forEach((controller) => {
      controller.updateDisplay();
    });
  }

  destroy() {
    // 清理文件输入元素
    if (this.fileInputs) {
      Object.values(this.fileInputs).forEach((fileInput) => {
        if (fileInput && fileInput.parentNode) {
          fileInput.parentNode.removeChild(fileInput);
        }
      });
      this.fileInputs = null;
    }

    // Remove from scene
    if (this.scene && this.mesh) {
      this.scene.remove(this.mesh);
    }

    // Dispose of geometry
    if (this.geometry) {
      this.geometry.dispose();
    }

    // Dispose of material
    if (this.material) {
      this.material.dispose();
    }

    // Dispose of textures
    Object.values(this.textures).forEach((texture) => {
      if (texture) {
        texture.dispose();
      }
    });

    // 清理自定义纹理
    if (this.customTextures) {
      Object.values(this.customTextures).forEach((texture) => {
        if (texture) {
          texture.dispose();
        }
      });
      this.customTextures = {};
    }

    // Clean up GUI
    if (this.folder) {
      this.folder.destroy();
      this.folder = null;
    }

    // Clear references
    this.mesh = null;
    this.geometry = null;
    this.material = null;
    this.textures = {};
    this.originalTextures = {};
    this.gui = null;
  }
}

// Export both named and default exports for flexibility
export { EarthDay };
export default EarthDay;
