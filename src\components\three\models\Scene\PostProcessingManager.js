import * as THREE from "three";
import { EffectComposer } from "three/examples/jsm/postprocessing/EffectComposer.js";
import { RenderPass } from "three/examples/jsm/postprocessing/RenderPass.js";
import { UnrealBloomPass } from "three/examples/jsm/postprocessing/UnrealBloomPass.js";
import { OutputPass } from "three/examples/jsm/postprocessing/OutputPass.js";
import { GUI } from "lil-gui";
import { ColorGrading } from "./ColorGrading.js";

/**
 * PostProcessingManager - 管理Three.js后处理效果
 * 提供辉光(Bloom)等后处理效果的统一管理
 */
class PostProcessingManager {
  constructor(scene, camera, renderer) {
    this.scene = scene;
    this.camera = camera;
    this.renderer = renderer;

    // 后处理组件
    this.composer = null;
    this.renderPass = null;
    this.bloomPass = null;
    this.colorGrading = null;
    this.outputPass = null;

    // 辉光参数
    this.bloomParams = {
      enabled: true,
      threshold: 0.85, // 辉光阈值 - 只有亮度超过此值的像素才会发光
      strength: 0.19, // 辉光强度
      radius: 0, // 辉光半径
      exposure: 1.0, // 曝光度
    };

    // GUI控制
    this.gui = null;
    this.folder = null;

    this.init();
  }

  init() {
    this.createComposer();
    this.createPasses();
    this.setupGUI();
  }

  /**
   * 创建EffectComposer
   */
  createComposer() {
    // 创建渲染目标
    const renderTarget = new THREE.WebGLRenderTarget(window.innerWidth, window.innerHeight, {
      type: THREE.HalfFloatType,
      format: THREE.RGBAFormat,
      colorSpace: THREE.SRGBColorSpace,
    });

    // 创建效果合成器
    this.composer = new EffectComposer(this.renderer);
    // this.composer = new EffectComposer(this.renderer, renderTarget);
  }

  /**
   * 创建后处理通道
   */
  createPasses() {
    // 1. 渲染通道 - 渲染场景到纹理
    this.renderPass = new RenderPass(this.scene, this.camera);
    this.composer.addPass(this.renderPass);

    // 2. 辉光通道
    this.bloomPass = new UnrealBloomPass(new THREE.Vector2(window.innerWidth, window.innerHeight), this.bloomParams.strength, this.bloomParams.radius, this.bloomParams.threshold);
    this.composer.addPass(this.bloomPass);
    this.bloomPass.enabled = this.bloomParams.enabled;

    // 3. 输出通道 - 将结果输出到屏幕
    this.outputPass = new OutputPass();
    this.composer.addPass(this.outputPass);

    // 设置曝光度
    this.renderer.toneMappingExposure = this.bloomParams.exposure;
  }

  /**
   * 设置GUI控制
   */
  setupGUI() {
    // 尝试获取现有的GUI实例，如果没有则创建新的
    try {
      // 检查是否已经有GUI实例
      const guiContainer = document.querySelector(".lil-gui");
      if (guiContainer && window.gui) {
        this.gui = window.gui;
      } else {
        this.gui = new GUI();
        window.gui = this.gui; // 保存到全局变量以便其他组件使用
      }
    } catch (error) {
      console.warn("GUI setup warning:", error);
      this.gui = new GUI();
    }

    // 创建后处理文件夹
    this.folder = this.gui.addFolder("🌟 后处理效果 (Post Processing)");
    this.folder.open();

    // 创建辉光控制子文件夹
    this.setupBloomGUI();

    // 创建颜色分级通道和控制
    this.setupColorGrading();
  }

  /**
   * 渲染后处理效果
   */
  render() {
    if (this.composer && this.bloomParams.enabled) {
      this.composer.render();
    } else {
      // 如果后处理被禁用，使用常规渲染
      this.composer.render();
      // this.renderer.render(this.scene, this.camera);
    }
  }

  /**
   * 处理窗口大小变化
   */
  onWindowResize(width, height) {
    if (this.composer) {
      this.composer.setSize(width, height);
    }
  }

  /**
   * 设置像素比
   */
  setPixelRatio(pixelRatio) {
    if (this.composer) {
      this.composer.setPixelRatio(pixelRatio);
    }
  }

  /**
   * 获取辉光参数
   */
  getBloomParams() {
    return { ...this.bloomParams };
  }

  /**
   * 设置辉光参数
   */
  setBloomParams(params) {
    Object.assign(this.bloomParams, params);

    if (this.bloomPass) {
      this.bloomPass.threshold = this.bloomParams.threshold;
      this.bloomPass.strength = this.bloomParams.strength;
      this.bloomPass.radius = this.bloomParams.radius;
      this.bloomPass.enabled = this.bloomParams.enabled;
    }

    if (this.renderer) {
      this.renderer.toneMappingExposure = this.bloomParams.exposure;
    }
  }

  /**
   * 启用/禁用辉光效果
   */
  setBloomEnabled(enabled) {
    this.bloomParams.enabled = enabled;
    if (this.bloomPass) {
      this.bloomPass.enabled = enabled;
    }
  }

  /**
   * 设置辉光GUI控制
   */
  setupBloomGUI() {
    if (!this.folder) return;

    const bloomFolder = this.folder.addFolder("✨ 辉光效果 (Bloom)");
    bloomFolder.open();

    // 辉光开关
    bloomFolder
      .add(this.bloomParams, "enabled")
      .name("启用辉光")
      .onChange((value) => {
        this.bloomPass.enabled = value;
      });

    // 辉光阈值
    bloomFolder
      .add(this.bloomParams, "threshold", 0.0, 1.0, 0.01)
      .name("辉光阈值")
      .onChange((value) => {
        this.bloomPass.threshold = value;
      });

    // 辉光强度
    bloomFolder
      .add(this.bloomParams, "strength", 0.0, 3.0, 0.01)
      .name("辉光强度")
      .onChange((value) => {
        this.bloomPass.strength = value;
      });

    // 辉光半径
    bloomFolder
      .add(this.bloomParams, "radius", 0.0, 1.0, 0.01)
      .name("辉光半径")
      .onChange((value) => {
        this.bloomPass.radius = value;
      });

    // 曝光度
    bloomFolder
      .add(this.bloomParams, "exposure", 0.1, 2.0, 0.01)
      .name("曝光度")
      .onChange((value) => {
        this.renderer.toneMappingExposure = value;
      });
  }

  /**
   * 设置颜色分级
   */
  setupColorGrading() {
    if (!this.composer || !this.gui || !this.folder) return;

    // 创建颜色分级实例
    this.colorGrading = new ColorGrading(this.composer, this.gui);

    // 初始化GUI控制
    this.colorGrading.initGui(this.folder);
  }

  /**
   * 获取颜色分级实例
   */
  getColorGrading() {
    return this.colorGrading;
  }

  /**
   * 销毁资源
   */
  destroy() {
    // 清理颜色分级
    if (this.colorGrading) {
      this.colorGrading.destroy();
    }

    if (this.composer) {
      this.composer.dispose();
    }

    if (this.folder && this.gui) {
      this.gui.removeFolder(this.folder);
    }

    // 清理引用
    this.composer = null;
    this.renderPass = null;
    this.bloomPass = null;
    this.colorGrading = null;
    this.outputPass = null;
    this.scene = null;
    this.camera = null;
    this.renderer = null;
  }
}

export { PostProcessingManager };
export default PostProcessingManager;
