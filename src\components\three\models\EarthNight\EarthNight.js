import * as THREE from "three";
import { EARTH_FRAGMENTS, EARTH_RADIUS, PATHS } from "../../../../constants";
import CustomShaderMaterial from "three-custom-shader-material/vanilla";
import { texturePreloader } from "../../utils/TexturePreloader.js";
import { GUI } from "lil-gui";

class EarthNight {
  constructor(scene, { renderer, enableGUI = true } = {}) {
    this.scene = scene;
    this.renderer = renderer;
    this.mesh = null;
    this.geometry = null;
    this.material = null;
    this.textures = {
      day: null,
      night: null,
    };
    this.uniforms = null;
    this.lightPos = new THREE.Vector3().setScalar(2);

    // GUI相关属性
    this.enableGUI = enableGUI;
    this.gui = null;
    this.folder = null;
    this.nightParams = {
      brightness: 1.0,
      contrast: 1.09,
      gamma: 0.89, // 伽马校正，<1.0让亮处更亮暗处更暗，>1.0相反
      redMultiplier: 0.85, // 红色通道倍数
      greenMultiplier: 0.85, // 绿色通道倍数
      blueMultiplier: 1.0, // 蓝色通道倍数
      // 昼夜分界线控制参数
      transitionStart: -0.1, // 过渡开始点
      transitionEnd: 0.1, // 过渡结束点
      nightOffset: 0.0, // 夜间区域偏移，负值扩大夜间区域，正值缩小夜间区域
    };

    // 纹理上传相关属性
    this.originalNightTexture = null; // 保存原始夜间纹理的引用
    this.customNightTexture = null; // 用户上传的自定义纹理

    this.init();
  }

  async init() {
    await this.loadTextures();
    this.createGeometry();
    this.createMaterial();
    this.createMesh();
    this.addToScene();

    // 初始化GUI控制面板
    if (this.enableGUI) {
      this.initGUI();
    }
  }

  async loadTextures() {
    try {
      // 优先使用预加载的纹理
      const preloadedTextures = texturePreloader.getAllTextures();

      if (preloadedTextures.earthMap && preloadedTextures.earthNight) {
        console.log("EarthNight: 使用预加载的纹理");

        this.textures.day = preloadedTextures.earthMap;
        this.textures.night = preloadedTextures.earthNight;
        // 保存原始夜间纹理的引用
        this.originalNightTexture = preloadedTextures.earthNight;
      } else {
        console.log("EarthNight: 预加载纹理不可用，使用异步加载");

        // 如果预加载纹理不可用，使用传统的异步加载方式
        const textureLoader = new THREE.TextureLoader();
        const [dayTexture, nightTexture] = await Promise.all([this.loadTexture(textureLoader, PATHS.earthMap), this.loadTexture(textureLoader, PATHS.earthNight)]);

        this.textures.day = dayTexture;
        this.textures.night = nightTexture;
        // 保存原始夜间纹理的引用
        this.originalNightTexture = nightTexture;
      }

      // Set color space for proper color rendering
      // this.textures.day.colorSpace = THREE.SRGBColorSpace; // Color texture needs sRGB
      // this.textures.night.colorSpace = THREE.SRGBColorSpace; // Color texture needs sRGB

      // Set anisotropy for better quality
      const maxAnisotropy = this.renderer?.capabilities?.getMaxAnisotropy?.() || 16;
      this.textures.day.anisotropy = maxAnisotropy;
      this.textures.night.anisotropy = maxAnisotropy;

      console.log("EarthNight: 纹理加载完成");
    } catch (error) {
      console.error("Error loading EarthNight textures:", error);
    }
  }

  loadTexture(loader, url) {
    return new Promise((resolve, reject) => {
      loader.load(
        url,
        (texture) => resolve(texture),
        undefined,
        (error) => reject(error)
      );
    });
  }

  createGeometry() {
    this.geometry = new THREE.SphereGeometry(EARTH_RADIUS, EARTH_FRAGMENTS, EARTH_FRAGMENTS);
  }

  createMaterial() {
    this.uniforms = {
      uDay: { value: this.textures.day },
      uNight: { value: this.textures.night },
      uLight: { value: this.lightPos },
      uNightBrightness: { value: this.nightParams.brightness }, // 亮度调整
      uNightContrast: { value: this.nightParams.contrast }, // 对比度调整
      uNightGamma: { value: this.nightParams.gamma }, // 伽马校正
      uNightRGBMultiplier: { value: new THREE.Vector3(this.nightParams.redMultiplier, this.nightParams.greenMultiplier, this.nightParams.blueMultiplier) }, // RGB通道调整
      // 昼夜分界线控制uniforms
      uTransitionStart: { value: this.nightParams.transitionStart },
      uTransitionEnd: { value: this.nightParams.transitionEnd },
      uNightOffset: { value: this.nightParams.nightOffset },
    };

    this.material = new CustomShaderMaterial({
      baseMaterial: THREE.MeshBasicMaterial,
      vertexShader: `
        uniform vec3 uLight;
        varying vec2 vUv2;
        varying float vDist;

        void main() {
          vUv2 = uv;

          // 将顶点位置转换到世界坐标系
          vec4 worldPosition213 = modelMatrix * vec4(position, 1.0);
          vec3 worldNormal = normalize(worldPosition213.xyz);

          // 计算从顶点到光源的方向
          vec3 lightDirection = normalize(uLight - worldPosition213.xyz);

          // 计算点积，用于确定光照强度
          float dotProduct = dot(worldNormal, lightDirection);

          // 直接使用点积值作为过渡参数
          vDist = dotProduct;
        }
      `,
      fragmentShader: `
        uniform sampler2D uNight;
        uniform vec3 uLight;
        uniform float uNightBrightness;
        uniform float uNightContrast;
        uniform float uNightGamma;
        uniform vec3 uNightRGBMultiplier;
        uniform float uTransitionStart;
        uniform float uTransitionEnd;
        uniform float uNightOffset;
        varying vec2 vUv2;
        varying float vDist;

        void main() {
          vec4 texNight = texture2D(uNight, vUv2);

          // 应用亮度和对比度调整
          // 对比度调整：(color - 0.5) * contrast + 0.5
          // 亮度调整：color * brightness
          texNight.rgb = ((texNight.rgb - 0.5) * uNightContrast + 0.5) * uNightBrightness;

          // 应用伽马校正 - 让亮的地方更亮，暗的地方更暗
          // gamma < 1.0: 增强对比度，亮处更亮，暗处更暗
          // gamma > 1.0: 减少对比度，整体变亮
          texNight.rgb = pow(max(texNight.rgb, 0.0), vec3(1.0 / uNightGamma));

          // 应用RGB通道调整
          // 分别调整红、绿、蓝三个颜色通道
          texNight.rgb *= uNightRGBMultiplier;

          vec4 clear = vec4(0, 0, 0, 0);

          // vDist是光照强度 [-1, 1]
          // vDist > 0: 面向光源（白天区域）
          // vDist < 0: 背离光源（黑夜区域）
          // vDist = 0: 昼夜分界线

          // 应用夜间区域偏移来调整昼夜分界线
          float adjustedVDist = vDist + uNightOffset;

          // 使用可调节的过渡参数创建平滑的昼夜过渡
          // uTransitionStart: 夜间区域完全显示的阈值
          // uTransitionEnd: 白天区域完全显示的阈值
          float fadeOut = smoothstep(uTransitionStart, uTransitionEnd, adjustedVDist);
          vec4 d = mix(texNight, clear, fadeOut);

          csm_DiffuseColor = d;
        }
      `,
      uniforms: this.uniforms,
      transparent: true,
    });
  }

  createMesh() {
    this.mesh = new THREE.Mesh(this.geometry, this.material);
    this.mesh.receiveShadow = true;
    const scale = 1.001;
    this.mesh.scale.set(scale, scale, scale);
    this.mesh.rotation.y = Math.PI;
    this.mesh.renderOrder = 0; // 与地球白天层相同的渲染顺序
  }

  addToScene() {
    if (this.scene && this.mesh) {
      // this.mesh.visible = false;
      this.scene.add(this.mesh);
    }
  }

  update(lightPos) {
    if (this.material && this.material.uniforms && lightPos) {
      this.material.uniforms.uLight.value = lightPos;
    }
  }

  /**
   * 设置夜晚纹理的亮度
   * @param {number} brightness - 亮度值，1.0为默认值，>1.0更亮，<1.0更暗
   */
  setNightBrightness(brightness) {
    this.nightParams.brightness = brightness;
    if (this.material && this.material.uniforms && this.material.uniforms.uNightBrightness) {
      this.material.uniforms.uNightBrightness.value = brightness;
    }
  }

  /**
   * 设置夜晚纹理的对比度
   * @param {number} contrast - 对比度值，1.0为默认值，>1.0对比度更高，<1.0对比度更低
   */
  setNightContrast(contrast) {
    this.nightParams.contrast = contrast;
    if (this.material && this.material.uniforms && this.material.uniforms.uNightContrast) {
      this.material.uniforms.uNightContrast.value = contrast;
    }
  }

  /**
   * 设置夜晚纹理的伽马校正
   * @param {number} gamma - 伽马值，1.0为默认值，<1.0让亮处更亮暗处更暗，>1.0相反
   */
  setNightGamma(gamma) {
    this.nightParams.gamma = gamma;
    if (this.material && this.material.uniforms && this.material.uniforms.uNightGamma) {
      this.material.uniforms.uNightGamma.value = gamma;
    }
  }

  /**
   * 设置夜晚纹理的RGB通道调整
   * @param {number} red - 红色通道倍数，1.0为默认值
   * @param {number} green - 绿色通道倍数，1.0为默认值
   * @param {number} blue - 蓝色通道倍数，1.0为默认值
   */
  setNightRGB(red, green, blue) {
    this.nightParams.redMultiplier = red;
    this.nightParams.greenMultiplier = green;
    this.nightParams.blueMultiplier = blue;

    if (this.material && this.material.uniforms && this.material.uniforms.uNightRGBMultiplier) {
      this.material.uniforms.uNightRGBMultiplier.value.set(red, green, blue);
    }
  }

  /**
   * 设置夜晚纹理的红色通道
   * @param {number} red - 红色通道倍数
   */
  setNightRed(red) {
    this.nightParams.redMultiplier = red;
    if (this.material && this.material.uniforms && this.material.uniforms.uNightRGBMultiplier) {
      this.material.uniforms.uNightRGBMultiplier.value.x = red;
    }
  }

  /**
   * 设置夜晚纹理的绿色通道
   * @param {number} green - 绿色通道倍数
   */
  setNightGreen(green) {
    this.nightParams.greenMultiplier = green;
    if (this.material && this.material.uniforms && this.material.uniforms.uNightRGBMultiplier) {
      this.material.uniforms.uNightRGBMultiplier.value.y = green;
    }
  }

  /**
   * 设置夜晚纹理的蓝色通道
   * @param {number} blue - 蓝色通道倍数
   */
  setNightBlue(blue) {
    this.nightParams.blueMultiplier = blue;
    if (this.material && this.material.uniforms && this.material.uniforms.uNightRGBMultiplier) {
      this.material.uniforms.uNightRGBMultiplier.value.z = blue;
    }
  }

  /**
   * 设置昼夜过渡的开始点
   * @param {number} start - 过渡开始点，通常为负值
   */
  setTransitionStart(start) {
    this.nightParams.transitionStart = start;
    if (this.material && this.material.uniforms && this.material.uniforms.uTransitionStart) {
      this.material.uniforms.uTransitionStart.value = start;
    }
  }

  /**
   * 设置昼夜过渡的结束点
   * @param {number} end - 过渡结束点，通常为正值
   */
  setTransitionEnd(end) {
    this.nightParams.transitionEnd = end;
    if (this.material && this.material.uniforms && this.material.uniforms.uTransitionEnd) {
      this.material.uniforms.uTransitionEnd.value = end;
    }
  }

  /**
   * 设置夜间区域偏移
   * @param {number} offset - 偏移值，负值扩大夜间区域，正值缩小夜间区域
   */
  setNightOffset(offset) {
    this.nightParams.nightOffset = offset;
    if (this.material && this.material.uniforms && this.material.uniforms.uNightOffset) {
      this.material.uniforms.uNightOffset.value = offset;
    }
  }

  /**
   * 设置昼夜分界线的所有参数
   * @param {number} start - 过渡开始点
   * @param {number} end - 过渡结束点
   * @param {number} offset - 夜间区域偏移
   */
  setDayNightTransition(start, end, offset = 0) {
    this.setTransitionStart(start);
    this.setTransitionEnd(end);
    this.setNightOffset(offset);
  }

  /**
   * 同时设置夜晚纹理的所有参数
   * @param {number} brightness - 亮度值
   * @param {number} contrast - 对比度值
   * @param {number} gamma - 伽马值（可选）
   * @param {Object} rgb - RGB调整对象 {r, g, b}（可选）
   */
  setNightAppearance(brightness, contrast, gamma = null, rgb = null) {
    this.setNightBrightness(brightness);
    this.setNightContrast(contrast);
    if (gamma !== null) {
      this.setNightGamma(gamma);
    }
    if (rgb !== null) {
      this.setNightRGB(rgb.r || 1.0, rgb.g || 1.0, rgb.b || 1.0);
    }
  }

  /**
   * 获取当前夜晚纹理的亮度值
   * @returns {number} 当前亮度值
   */
  getNightBrightness() {
    return this.material?.uniforms?.uNightBrightness?.value || 1.0;
  }

  /**
   * 获取当前夜晚纹理的对比度值
   * @returns {number} 当前对比度值
   */
  getNightContrast() {
    return this.material?.uniforms?.uNightContrast?.value || 1.0;
  }

  /**
   * 获取当前夜晚纹理的伽马值
   * @returns {number} 当前伽马值
   */
  getNightGamma() {
    return this.material?.uniforms?.uNightGamma?.value || 1.0;
  }

  /**
   * 获取当前夜晚纹理的RGB调整值
   * @returns {Object} RGB调整值 {r, g, b}
   */
  getNightRGB() {
    const rgb = this.material?.uniforms?.uNightRGBMultiplier?.value;
    return rgb ? { r: rgb.x, g: rgb.y, b: rgb.z } : { r: 1.0, g: 1.0, b: 1.0 };
  }

  /**
   * 获取当前昼夜过渡开始点
   * @returns {number} 过渡开始点
   */
  getTransitionStart() {
    return this.material?.uniforms?.uTransitionStart?.value || -0.1;
  }

  /**
   * 获取当前昼夜过渡结束点
   * @returns {number} 过渡结束点
   */
  getTransitionEnd() {
    return this.material?.uniforms?.uTransitionEnd?.value || 0.1;
  }

  /**
   * 获取当前夜间区域偏移
   * @returns {number} 偏移值
   */
  getNightOffset() {
    return this.material?.uniforms?.uNightOffset?.value || 0.0;
  }

  /**
   * 获取昼夜分界线的所有参数
   * @returns {Object} 包含start, end, offset的对象
   */
  getDayNightTransition() {
    return {
      start: this.getTransitionStart(),
      end: this.getTransitionEnd(),
      offset: this.getNightOffset(),
    };
  }

  /**
   * 上传并替换夜间纹理
   * @param {File} file - 用户上传的图片文件
   * @returns {Promise<boolean>} 是否成功替换纹理
   */
  async uploadNightTexture(file) {
    try {
      // 验证文件类型
      if (!file || !file.type.startsWith("image/")) {
        throw new Error("请选择有效的图片文件");
      }

      // 创建文件URL
      const imageUrl = URL.createObjectURL(file);

      // 加载新纹理
      const textureLoader = new THREE.TextureLoader();
      const newTexture = await this.loadTextureFromUrl(textureLoader, imageUrl);

      // 设置纹理属性
      const maxAnisotropy = this.renderer?.capabilities?.getMaxAnisotropy?.() || 16;
      newTexture.anisotropy = maxAnisotropy;
      newTexture.wrapS = THREE.RepeatWrapping;
      newTexture.wrapT = THREE.RepeatWrapping;

      // 释放之前的自定义纹理（如果存在）
      if (this.customNightTexture) {
        this.customNightTexture.dispose();
      }

      // 保存新的自定义纹理
      this.customNightTexture = newTexture;

      // 更新材质中的纹理
      this.textures.night = newTexture;
      if (this.material && this.material.uniforms && this.material.uniforms.uNight) {
        this.material.uniforms.uNight.value = newTexture;
      }

      // 清理临时URL
      URL.revokeObjectURL(imageUrl);

      console.log("夜间纹理上传成功");
      return true;
    } catch (error) {
      console.error("夜间纹理上传失败:", error);
      throw error;
    }
  }

  /**
   * 从URL加载纹理（Promise版本）
   * @param {THREE.TextureLoader} loader - 纹理加载器
   * @param {string} url - 纹理URL
   * @returns {Promise<THREE.Texture>} 加载的纹理
   */
  loadTextureFromUrl(loader, url) {
    return new Promise((resolve, reject) => {
      loader.load(
        url,
        (texture) => resolve(texture),
        undefined,
        (error) => reject(error)
      );
    });
  }

  /**
   * 恢复原始夜间纹理
   */
  restoreOriginalNightTexture() {
    if (!this.originalNightTexture) {
      console.warn("没有找到原始夜间纹理");
      return;
    }

    // 释放自定义纹理
    if (this.customNightTexture) {
      this.customNightTexture.dispose();
      this.customNightTexture = null;
    }

    // 恢复原始纹理
    this.textures.night = this.originalNightTexture;
    if (this.material && this.material.uniforms && this.material.uniforms.uNight) {
      this.material.uniforms.uNight.value = this.originalNightTexture;
    }

    console.log("已恢复原始夜间纹理");
  }

  /**
   * 初始化GUI控制面板
   */
  initGUI() {
    if (!this.enableGUI) {
      return;
    }

    try {
      // 获取或创建全局GUI实例
      if (window.gui) {
        this.gui = window.gui;
      } else {
        this.gui = new GUI();
        window.gui = this.gui;
      }

      // 创建夜晚纹理控制文件夹
      this.folder = this.gui.addFolder("🌙 夜晚纹理 (Night Texture)");
      this.folder.open();

      this.setupGUIControls();
    } catch (error) {
      console.warn("EarthNight GUI setup warning:", error);
    }
  }

  /**
   * 设置GUI控制项
   */
  setupGUIControls() {
    if (!this.folder) return;

    // 亮度控制
    this.folder
      .add(this.nightParams, "brightness", 0.1, 3.0, 0.1)
      .name("🔆 亮度 (Brightness)")
      .onChange((value) => {
        this.setNightBrightness(value);
      });

    // 对比度控制
    this.folder
      .add(this.nightParams, "contrast", 0.1, 3, 0.01)
      .name("🎭 对比度 (Contrast)")
      .onChange((value) => {
        this.setNightContrast(value);
      });

    // 伽马校正控制
    this.folder
      .add(this.nightParams, "gamma", 0.3, 2.5, 0.01)
      .name("⚡ 伽马校正 (Gamma)")
      .onChange((value) => {
        this.setNightGamma(value);
      });

    // 创建RGB调整子文件夹
    const rgbFolder = this.folder.addFolder("🎨 RGB通道调整 (RGB Channels)");
    rgbFolder.open();

    // 红色通道控制
    rgbFolder
      .add(this.nightParams, "redMultiplier", 0.0, 2.0, 0.01)
      .name("🔴 红色 (Red)")
      .onChange((value) => {
        this.setNightRed(value);
      });

    // 绿色通道控制
    rgbFolder
      .add(this.nightParams, "greenMultiplier", 0.0, 2.0, 0.01)
      .name("🟢 绿色 (Green)")
      .onChange((value) => {
        this.setNightGreen(value);
      });

    // 蓝色通道控制
    rgbFolder
      .add(this.nightParams, "blueMultiplier", 0.0, 2.0, 0.01)
      .name("🔵 蓝色 (Blue)")
      .onChange((value) => {
        this.setNightBlue(value);
      });

    // 创建昼夜分界线调整子文件夹
    const transitionFolder = this.folder.addFolder("🌗 昼夜分界线 (Day-Night Transition)");
    transitionFolder.open();

    // 过渡开始点控制
    transitionFolder
      .add(this.nightParams, "transitionStart", -1.0, 1.0, 0.01)
      .name("🌅 过渡开始 (Transition Start)")
      .onChange((value) => {
        this.setTransitionStart(value);
      });

    // 过渡结束点控制
    transitionFolder
      .add(this.nightParams, "transitionEnd", -1.0, 1.0, 0.01)
      .name("🌇 过渡结束 (Transition End)")
      .onChange((value) => {
        this.setTransitionEnd(value);
      });

    // 夜间区域偏移控制
    transitionFolder
      .add(this.nightParams, "nightOffset", -1.0, 1.0, 0.01)
      .name("🌙 夜间偏移 (Night Offset)")
      .onChange((value) => {
        this.setNightOffset(value);
      });

    // 添加预设按钮
    const presetControls = {
      halfAndHalf: () => {
        // 让昼夜各占一半的设置
        this.nightParams.transitionStart = -0.1;
        this.nightParams.transitionEnd = 0.1;
        this.nightParams.nightOffset = 0.0;
        this.setDayNightTransition(-0.1, 0.1, 0.0);
        transitionFolder.controllersRecursive().forEach((controller) => {
          controller.updateDisplay();
        });
      },
      moreNight: () => {
        // 扩大夜间区域的设置
        this.nightParams.transitionStart = -0.3;
        this.nightParams.transitionEnd = 0.3;
        this.nightParams.nightOffset = -0.2;
        this.setDayNightTransition(-0.3, 0.3, -0.2);
        transitionFolder.controllersRecursive().forEach((controller) => {
          controller.updateDisplay();
        });
      },
      lessNight: () => {
        // 缩小夜间区域的设置
        this.nightParams.transitionStart = 0.1;
        this.nightParams.transitionEnd = 0.5;
        this.nightParams.nightOffset = 0.2;
        this.setDayNightTransition(0.1, 0.5, 0.2);
        transitionFolder.controllersRecursive().forEach((controller) => {
          controller.updateDisplay();
        });
      },
    };

    transitionFolder.add(presetControls, "halfAndHalf").name("⚖️ 昼夜各半");
    transitionFolder.add(presetControls, "moreNight").name("🌃 扩大夜间");
    transitionFolder.add(presetControls, "lessNight").name("☀️ 缩小夜间");

    // 重置按钮
    const resetControls = {
      reset: () => {
        // 重置所有参数到默认值
        this.nightParams.brightness = 1.0;
        this.nightParams.contrast = 1.03;
        this.nightParams.gamma = 0.87;
        this.nightParams.redMultiplier = 0.85;
        this.nightParams.greenMultiplier = 0.85;
        this.nightParams.blueMultiplier = 1.0;
        this.nightParams.transitionStart = -0.1;
        this.nightParams.transitionEnd = 0.1;
        this.nightParams.nightOffset = 0.0;

        // 应用所有设置
        this.setNightBrightness(1.0);
        this.setNightContrast(1.03);
        this.setNightGamma(0.87);
        this.setNightRGB(0.85, 0.85, 1.0);
        this.setDayNightTransition(-0.1, 0.1, 0.0);

        // 刷新GUI显示
        this.folder.controllersRecursive().forEach((controller) => {
          controller.updateDisplay();
        });
      },
    };

    this.folder.add(resetControls, "reset").name("🔄 重置默认值");

    // 添加纹理上传控件
    this.setupTextureUploadControls();
  }

  /**
   * 设置纹理上传控件
   */
  setupTextureUploadControls() {
    if (!this.folder) return;

    // 创建纹理上传子文件夹
    const uploadFolder = this.folder.addFolder("📁 纹理上传 (Texture Upload)");
    uploadFolder.open();

    // 创建隐藏的文件输入元素
    const fileInput = document.createElement("input");
    fileInput.type = "file";
    fileInput.accept = "image/*";
    fileInput.style.display = "none";
    document.body.appendChild(fileInput);

    // 上传按钮控件
    const uploadControls = {
      uploadTexture: () => {
        fileInput.click();
      },
      restoreOriginal: () => {
        this.restoreOriginalNightTexture();
      },
    };

    // 文件选择事件处理
    fileInput.addEventListener("change", async (event) => {
      const file = event.target.files[0];
      if (file) {
        try {
          // 显示加载状态（可以在这里添加加载指示器）
          console.log("开始上传夜间纹理...");

          await this.uploadNightTexture(file);

          // 上传成功提示
          console.log("夜间纹理上传完成！");

          // 可以在这里添加成功提示的UI反馈
          this.showUploadSuccess();
        } catch (error) {
          console.error("纹理上传失败:", error);

          // 可以在这里添加错误提示的UI反馈
          this.showUploadError(error.message);
        }

        // 清空文件输入，允许重复选择同一文件
        fileInput.value = "";
      }
    });

    // 添加上传按钮
    uploadFolder.add(uploadControls, "uploadTexture").name("📤 上传夜间纹理");

    // 添加恢复原始纹理按钮
    uploadFolder.add(uploadControls, "restoreOriginal").name("🔄 恢复原始纹理");

    // 添加使用说明
    const infoFolder = uploadFolder.addFolder("ℹ️ 使用说明");
    const info = {
      info1: "支持 JPG, PNG, WebP 等图片格式",
      info2: "建议使用 2048x1024 或更高分辨率",
      info3: "纹理会自动应用到地球夜间面",
      info4: "可随时恢复到原始纹理",
    };

    // 由于lil-gui不直接支持只读文本，我们使用按钮来显示信息
    Object.entries(info).forEach(([key, text]) => {
      const infoControl = {};
      infoControl[key] = () => {
        console.log(text);
      };
      infoFolder.add(infoControl, key).name(text).disable();
    });

    // 保存文件输入引用以便清理
    this.fileInput = fileInput;
  }

  /**
   * 显示上传成功提示
   */
  showUploadSuccess() {
    // 简单的控制台提示，可以扩展为更丰富的UI反馈
    console.log("✅ 夜间纹理上传成功！");

    // 可以在这里添加更复杂的UI反馈，比如临时显示成功消息
    if (typeof window !== "undefined" && window.alert) {
      // 在浏览器环境中显示简单提示
      setTimeout(() => {
        console.log("夜间纹理已成功替换");
      }, 100);
    }
  }

  /**
   * 显示上传错误提示
   * @param {string} message - 错误消息
   */
  showUploadError(message) {
    console.error("❌ 纹理上传失败:", message);

    // 可以在这里添加更复杂的UI反馈
    if (typeof window !== "undefined" && window.alert) {
      // 在浏览器环境中显示错误提示
      setTimeout(() => {
        console.error("上传失败:", message);
      }, 100);
    }
  }

  destroy() {
    // 清理文件输入元素
    if (this.fileInput && this.fileInput.parentNode) {
      this.fileInput.parentNode.removeChild(this.fileInput);
      this.fileInput = null;
    }

    // 清理GUI
    if (this.folder && this.gui) {
      try {
        this.gui.removeFolder(this.folder);
      } catch (error) {
        console.warn("Error removing GUI folder:", error);
      }
    }

    // Remove from scene
    if (this.scene && this.mesh) {
      this.scene.remove(this.mesh);
    }

    // Dispose of geometry
    if (this.geometry) {
      this.geometry.dispose();
    }

    // Dispose of material
    if (this.material) {
      this.material.dispose();
    }

    // Dispose of textures
    Object.values(this.textures).forEach((texture) => {
      if (texture) {
        texture.dispose();
      }
    });

    // 清理自定义纹理
    if (this.customNightTexture) {
      this.customNightTexture.dispose();
      this.customNightTexture = null;
    }

    // Clear references
    this.mesh = null;
    this.geometry = null;
    this.material = null;
    this.textures = {};
    this.uniforms = null;
    this.gui = null;
    this.folder = null;
    this.originalNightTexture = null;
  }
}

// Export both named and default exports for flexibility
export { EarthNight };
export default EarthNight;
