<template>
  <div class="wrapper">
    <div class="scene">
      <div class="globe" ref="containerRef" style="width: 100%; height: 100%">
        <!-- Scene is now created imperatively in onMounted, not as a Vue component -->
      </div>
    </div>
    <Home />

    <transition name="fade-out">
      <div v-if="showLoading" class="loading">
        <Loading :progress="loadingProgress" />
      </div>
    </transition>

    <div class="controls">
      <Controls :time="state.realTime + state.offset" :prevOffsetMsRef="prevOffsetMsRef" @change-offset="handleChangeOffset" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, provide } from "vue";
import Controls from "./components/Controls.vue";
import { getSunCoords } from "./utils";
import { Scene } from "./components/three/models/Scene/Scene.js";
import autofit from "autofit.js";
import Home from "./views/Home.vue";
import { usedata } from "./store/data";

import Loading from "./views/Loading.vue";

// 初始化数据
const dataStore = usedata();
dataStore.getData();
// 初始化 WebSocket 连接
dataStore.getWss();

const state = reactive({
  realTime: Date.now(),
  offset: 0,
});

const sunCoordsRef = ref({ lat: 0, lng: 0 });
const prevOffsetMsRef = ref({ current: 0 });
const containerRef = ref(null);

// UI动画控制状态
const uiAnimationTriggered = ref(false);

// 加载状态控制
const showLoading = ref(true);
const loadingProgress = ref(0);

// 提供给子组件的UI动画触发状态
provide("uiAnimationTriggered", uiAnimationTriggered);

// Initialize the scene when component mounts
onMounted(async () => {
  autofit.init();
  try {
    // Initialize sun position before creating the scene
    getSunPosition();

    // Make sure we have the DOM element
    if (containerRef.value) {
      // Create Scene singleton instance
      const scene = Scene.createInstance(containerRef.value, {
        sunCoordsRef: sunCoordsRef,
        onLoad: handleLoad,
      });

      // 监听纹理加载进度事件
      if (scene) {
        // 监听纹理加载进度
        scene.addEventListener("textureLoadProgress", (data) => {
          console.log("纹理加载进度:", data.progress.toFixed(1) + "%");
          loadingProgress.value = data.progress;
        });

        // 监听纹理加载完成
        scene.addEventListener("textureLoadComplete", (data) => {
          console.log("纹理加载完成，隐藏Loading界面");
          // 延迟一点时间再隐藏Loading，让用户看到100%的进度
          setTimeout(() => {
            showLoading.value = false;
          }, 500);
        });

        // 监听纹理加载错误
        scene.addEventListener("textureLoadError", (data) => {
          console.error("纹理加载失败:", data.url);
          // 即使加载失败也隐藏Loading界面
          setTimeout(() => {
            showLoading.value = false;
          }, 1000);
        });

        // 监听UI动画触发事件
        scene.addEventListener("uiAnimationTrigger", (data) => {
          console.log("UI动画触发事件接收到:", data);
          uiAnimationTriggered.value = true;
        });
      }
    }
  } catch (error) {
    console.error("Failed to load Scene:", error);
  }
});

// Cleanup function to destroy the scene when component unmounts
onUnmounted(() => {
  dataStore.closeWss();
  const scene = Scene.getInstance();
  if (scene) {
    scene.destroy();
  }
});

const handleChangeOffset = (offset) => {
  state.offset = offset;
  getSunPosition();
};

const getSunPosition = () => {
  // return;

  const coords = getSunCoords(new Date(state.realTime + state.offset));
  // console.log("🚀 ~ coords:", coords);
  sunCoordsRef.value = { lat: coords.lat, lng: coords.lng };
  sunCoordsRef.value = { lat: 21, lng: 158 };
};

const handleLoad = () => {
  getSunPosition();
};
</script>

<style scoped>
.wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.scene {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.globe {
  width: 100%;
  height: 100%;
}

.controls {
  position: absolute;
  bottom: 0px;
  left: 0px;
  width: 100%;
  height: 100px;
  z-index: 100;
}
/* 淡出动画样式 */
.fade-out-enter-active,
.fade-out-leave-active {
  transition: opacity 0.8s ease-out;
}

.fade-out-enter-from,
.fade-out-leave-to {
  opacity: 0;
}

.loading {
  position: absolute;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 666;
  background-color: rgb(0, 0, 0);
}
</style>
